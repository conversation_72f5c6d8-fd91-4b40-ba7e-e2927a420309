#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON合并工具
将通用NTR.json的内容合并到v0.6.json中
"""

import json
import argparse
import sys
from pathlib import Path


def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 不存在")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: 文件 {file_path} 不是有效的JSON格式: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取文件 {file_path} 时发生错误: {e}")
        sys.exit(1)


def save_json_file(data, file_path, backup=True):
    """保存JSON文件"""
    try:
        # 创建备份
        if backup and Path(file_path).exists():
            backup_path = f"{file_path}.backup"
            Path(file_path).rename(backup_path)
            print(f"已创建备份文件: {backup_path}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"已保存合并后的文件: {file_path}")
    except Exception as e:
        print(f"错误: 保存文件 {file_path} 时发生错误: {e}")
        sys.exit(1)


def merge_entries(target_data, source_data):
    """合并entries"""
    if 'entries' not in target_data:
        target_data['entries'] = {}
    
    if 'entries' not in source_data:
        print("警告: 源文件中没有找到 'entries' 字段")
        return target_data
    
    # 找到目标文件中最大的key值
    max_key = 0
    for key in target_data['entries'].keys():
        try:
            key_num = int(key)
            max_key = max(max_key, key_num)
        except ValueError:
            continue
    
    # 合并源文件的entries
    added_count = 0
    for source_key, source_entry in source_data['entries'].items():
        # 生成新的key
        max_key += 1
        new_key = str(max_key)
        
        # 复制条目并更新uid
        new_entry = source_entry.copy()
        new_entry['uid'] = max_key
        
        # 添加到目标数据
        target_data['entries'][new_key] = new_entry
        added_count += 1
        
        print(f"已添加条目: {source_entry.get('comment', '无注释')} (新ID: {new_key})")
    
    print(f"总共添加了 {added_count} 个条目")
    return target_data


def main():
    parser = argparse.ArgumentParser(description='合并JSON文件')
    parser.add_argument('source', help='源文件路径 (通用NTR.json)')
    parser.add_argument('target', help='目标文件路径 (v0.6.json)')
    parser.add_argument('--no-backup', action='store_true', help='不创建备份文件')
    parser.add_argument('--output', '-o', help='输出文件路径 (默认覆盖目标文件)')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not Path(args.source).exists():
        print(f"错误: 源文件 {args.source} 不存在")
        sys.exit(1)
    
    if not Path(args.target).exists():
        print(f"错误: 目标文件 {args.target} 不存在")
        sys.exit(1)
    
    # 加载文件
    print(f"正在加载源文件: {args.source}")
    source_data = load_json_file(args.source)
    
    print(f"正在加载目标文件: {args.target}")
    target_data = load_json_file(args.target)
    
    # 合并数据
    print("正在合并数据...")
    merged_data = merge_entries(target_data, source_data)
    
    # 确定输出文件路径
    output_path = args.output if args.output else args.target
    
    # 保存合并后的文件
    save_json_file(merged_data, output_path, backup=not args.no_backup)
    
    print("合并完成!")


if __name__ == '__main__':
    main()
