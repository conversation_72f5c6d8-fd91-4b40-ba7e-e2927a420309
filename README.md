# JSON合并工具

这个工具用于将两个JSON文件的内容进行合并，特别适用于合并包含"entries"字段的JSON文件。

## 功能特点

- 自动处理ID冲突，为新添加的条目分配新的ID
- 自动创建备份文件
- 支持自定义输出路径
- 完整的错误处理和用户友好的提示信息

## 使用方法

### 基本用法

```bash
python3 merge_json.py 源文件.json 目标文件.json
```

### 示例

将`通用NTR.json`的内容合并到`6.json`中：

```bash
python3 merge_json.py "通用NTR.json" "6.json"
```

### 高级选项

```bash
# 不创建备份文件
python3 merge_json.py "通用NTR.json" "6.json" --no-backup

# 指定输出文件路径
python3 merge_json.py "通用NTR.json" "6.json" -o "merged_output.json"

# 查看帮助信息
python3 merge_json.py --help
```

## 合并规则

1. **ID处理**: 脚本会自动找到目标文件中最大的数字ID，然后为源文件中的每个条目分配新的递增ID
2. **UID更新**: 每个新添加的条目的`uid`字段会被更新为新的ID值
3. **备份**: 默认会为目标文件创建`.backup`后缀的备份文件
4. **完整性**: 保持源文件中条目的所有原始属性和结构

## 输出示例

```
正在加载源文件: 通用NTR.json
正在加载目标文件: 6.json
正在合并数据...
已添加条目: 狀態 (新ID: 250)
已添加条目: 露出玩法 (新ID: 251)
已添加条目: 語氣 (新ID: 252)
...
总共添加了 17 个条目
已创建备份文件: 6.json.backup
已保存合并后的文件: 6.json
合并完成!
```

## 文件结构要求

源文件和目标文件都应该包含以下结构：

```json
{
  "entries": {
    "0": { ... },
    "1": { ... },
    ...
  }
}
```

## 错误处理

脚本包含完整的错误处理：
- 文件不存在检查
- JSON格式验证
- 读写权限检查
- 异常情况处理

## 注意事项

- 合并前请确保重要文件已备份
- 脚本会自动创建备份，但建议手动备份重要数据
- 合并后的文件会保持原有的JSON格式和缩进
